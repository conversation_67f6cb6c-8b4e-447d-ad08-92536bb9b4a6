package ${packageName};

import ${servicePackage}.${serviceName};
import ${dtoPackage}.${dtoName};
import ${domainPackage}.${entityName};
import ${repositoryPackage}.${repositoryName};
import ${mapperPackage}.${mapperName};
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link ${entityName}}.
 */
@Service
@Transactional
public class ${serviceImplName} implements ${serviceName} {

    private final Logger log = LoggerFactory.getLogger(${serviceImplName}.class);

    private final ${repositoryName} ${repositoryVarName};
    private final ${mapperName} ${mapperVarName};

    public ${serviceImplName}(${repositoryName} ${repositoryVarName}, ${mapperName} ${mapperVarName}) {
        this.${repositoryVarName} = ${repositoryVarName};
        this.${mapperVarName} = ${mapperVarName};
    }

    @Override
    public ${dtoName} save(${dtoName} ${entityNameLower}DTO) {
        log.debug("Request to save ${entityName} : {}", ${entityNameLower}DTO);
        ${entityName} ${entityNameLower} = ${mapperVarName}.toEntity(${entityNameLower}DTO);
        ${entityNameLower} = ${repositoryVarName}.save(${entityNameLower});
        return ${mapperVarName}.toDto(${entityNameLower});
    }

    @Override
    public ${dtoName} update(${dtoName} ${entityNameLower}DTO) {
        log.debug("Request to update ${entityName} : {}", ${entityNameLower}DTO);
        ${entityName} ${entityNameLower} = ${mapperVarName}.toEntity(${entityNameLower}DTO);
        ${entityNameLower} = ${repositoryVarName}.save(${entityNameLower});
        return ${mapperVarName}.toDto(${entityNameLower});
    }

    @Override
    public Optional<${dtoName}> partialUpdate(${dtoName} ${entityNameLower}DTO) {
        log.debug("Request to partially update ${entityName} : {}", ${entityNameLower}DTO);

        return ${repositoryVarName}
            .findById(${entityNameLower}DTO.getId())
            .map(existing${entityName} -> {
                ${mapperVarName}.partialUpdate(existing${entityName}, ${entityNameLower}DTO);
                return existing${entityName};
            })
            .map(${repositoryVarName}::save)
            .map(${mapperVarName}::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<${dtoName}> findAll() {
        log.debug("Request to get all ${entityName}s");
        return ${repositoryVarName}.findAll().stream()
            .map(${mapperVarName}::toDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<${dtoName}> findAll(Pageable pageable) {
        log.debug("Request to get all ${entityName}s with pagination");
        return ${repositoryVarName}.findAll(pageable)
            .map(${mapperVarName}::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<${dtoName}> findOne(${idType} id) {
        log.debug("Request to get ${entityName} : {}", id);
        return ${repositoryVarName}.findById(id)
            .map(${mapperVarName}::toDto);
    }

    @Override
    public void delete(${idType} id) {
        log.debug("Request to delete ${entityName} : {}", id);
        ${repositoryVarName}.deleteById(id);
    }

    @Override
    public void deleteByIds(List<${idType}> ids) {
        log.debug("Request to delete ${entityName}s by ids : {}", ids);
        ${repositoryVarName}.deleteAllById(ids);
    }

    @Override
    @Transactional(readOnly = true)
    public long count() {
        log.debug("Request to count ${entityName}s");
        return ${repositoryVarName}.count();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsById(${idType} id) {
        log.debug("Request to check if ${entityName} exists : {}", id);
        return ${repositoryVarName}.existsById(id);
    }
}